# 密码管理系统

基于Vue的现代化密码管理系统，为运维团队提供安全便捷的密码管理解决方案。

## 功能特性

- **主机密码管理**：集中管理服务器和主机账号密码
- **密码策略管理**：支持多种安全策略，适应不同安全需求
- **定时更新任务**：支持自动化密码轮换，确保密码安全
- **紧急重置功能**：应对安全事件的紧急密码处理机制
- **批量操作支持**：高效管理多台主机密码

## 技术栈

- **前端框架**：Vue.js 3
- **状态管理**：Vuex 4
- **路由管理**：Vue Router 4
- **样式方案**：Tailwind CSS
- **图标方案**：Font Awesome

## 快速开始

### 前置条件

- Node.js (>= 14.x)
- npm 或 yarn

### 安装步骤

1. 克隆项目
```bash
git clone https://github.com/yourname/password-management.git
cd password-management
```

2. 安装依赖
```bash
npm install
# 或
yarn install
```

3. 启动开发服务器
```bash
npm run serve
# 或
yarn serve
```

4. 构建生产版本
```bash
npm run build
# 或
yarn build
```

## 项目结构

```
src/
├── assets/          # 静态资源文件
│   └── styles/      # 全局样式
├── components/      # 通用组件
├── router/          # 路由配置
├── store/           # Vuex状态管理
├── utils/           # 工具类
├── views/           # 页面组件
├── App.vue          # 根组件
└── main.js          # 应用入口
```

## 核心模块

### 主机管理

提供主机列表、状态监控和密码更新功能，支持单台主机操作和批量操作。

### 密码策略

管理多种密码策略，支持配置密码复杂度、有效期、轮换规则等。

### 定时任务

创建和管理自动化密码更新任务，支持多种调度选项和执行计划。

## 安全特性

- 密码强度实时评估
- 密码策略合规性检查
- 密码历史记录管理
- 操作审计日志

## 环境配置

系统支持多环境部署，可通过环境变量或配置文件自定义：

- 开发环境 (development)
- 测试环境 (test)
- 生产环境 (production)

## 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/amazing-feature`)
3. 提交更改 (`git commit -m 'Add amazing feature'`)
4. 推送到分支 (`git push origin feature/amazing-feature`)
5. 创建 Pull Request

